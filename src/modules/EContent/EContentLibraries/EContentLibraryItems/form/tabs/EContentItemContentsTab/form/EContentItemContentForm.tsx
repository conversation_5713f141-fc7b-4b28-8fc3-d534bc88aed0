import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  filter,
  includes,
  isEmpty,
  join,
  keyBy,
  map,
  pick,
  reduce,
  split,
  set,
  get,
  omit,
  concat,
  orderBy,
  forEach,
  groupBy,
  round,
  difference,
  uniq,
} from 'lodash';
import classnames from 'classnames';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import { useQuery } from 'react-apollo';
import { ApolloQueryResult } from 'apollo-client';

import styles from './EContentItemContentForm.scss';
import useT from '../../../../../../../../common/components/utils/Translations/useT';
import EntityForm from '../../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import StatusWithDraftField from '../../../../../../../../common/components/containers/EntityForm/fields/StatusWithDraftField';
import TextAreaField from '../../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import IEContentContent from '../../../../../../../../common/abstract/EContent/IEContentContent';
import { DEFAULT_WRAPPER_CLASS_NAMES } from '../../../../../../../../common/components/containers/EntityForm/internal/EntityFieldContent';
import IEContentResource from '../../../../../../../../common/abstract/EContent/IEContentResource';
import LanguageSelectorField from '../../../../../../../../common/components/containers/EntityForm/fields/LanguageSelectorField';
import { IEContentResourceXAttribute } from '../../../../../../../../common/abstract/EContent/IEContentResourceXAttribute';
import eContentResourceXLanguages from '../../../../../../../../common/data/eContent/eContentResourceXLanguages.graphql';
import eContentItemContentsSequence from '../../../../../../../../common/data/eContent/eContentItemContentsSequence.graphql';
import attributeTypes, {
  MainSubIds,
  questionMultipleType,
} from '../../../../../../../../model/EContentResourceXAttributeTypes';
import IEContentClob from '../../../../../../../../common/abstract/EContent/IEContentClob';
import cookEContentResourceXAttributes from '../../../../../../../../common/utils/cookEContentResourceXAttributes';
import { MANDATORY } from '../../../../../../../../model/RuleType';
import TagInputField from '../../../../../../../../common/components/containers/EntityForm/fields/TagInputField';
import StatusWithDraft, {
  Active,
} from '../../../../../../../../model/StatusWithDraft';
import TextSectionArrayField from './TextSectionArrayField';
import DocumentsSectionArrayField from './DocumentsSectionArrayField';
import ImagesSectionArrayField from './ImagesSectionArrayField';
import VideoFilesSectionArrayField from './VideoFilesSectionArrayField';
import AudioFilesSectionArrayField from './AudioFilesSectionArrayField';
import UrlSectionArrayField from './UrlSectionArrayField';
import QuestionSectionArrayField from './QuestionSectionArrayField';
import Notifications from '../../../../../../../../common/utils/Notifications';
import getGqlOperationName from '../../../../../../../../common/utils/getGqlOperationName';
import Tabs from '../../../../../../../../common/components/utils/Tabs';
import NumberField from '../../../../../../../../common/components/containers/EntityForm/fields/NumberField';
import { ONE, ZERO } from '../../../../../../../../common/const';
import {
  format,
  ValidatedAt,
} from '../../../../../../../../common/utils/dateTime';
import EditSessionContext from '../../../../../../../../common/components/containers/EditSessionProvider/EditSessionContext';
import useEditorImageProcess from '../../../../../../../../common/components/controls/base/TextEditor/useEditorImageProcess';
import useStorageSettings from '../../../../../../../../common/components/controls/base/TextEditor/useStorageSettings';

import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../fsCategories';
import useCurrentUser from '../../../../../../../../common/data/hooks/useCurrentUser';
import useFsClient from '../../../../../../../../common/data/hooks/useFsClient';
import ContentItemTitleWithArrows from './ContentItemTitleWithArrows';
import useEntityFormContext from '../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import EContentItemLogTab from '../EContentItemLogTab';

const WITH_FRAMEWORK = 'col-lg-8 col-md-6 col-sm-12 col-xs-12';
const THREE_FIELDS_IN_ROW = 3;
const FOUR_FIELDS_IN_ROW = 4;
const TWO = 2;
const POINT_ONE = 0.01;

export interface IEContentContentEntity extends IEContentContent {
  resourceId?: number;
  languageId?: number;
}

export interface IEContentItemContentForm {
  entity: IEContentContent;
  onGoBack: () => void;
  onSubmit: (values: IEContentContentEntity) => void;
  resource: IEContentResource;
  goToContents: () => void;
  goToCreate: () => void;
  goToCreateItem: () => void;
  usedKeywords?: string[];
  fromPage?: string;
  refetchTranslationSupport?: () => Promise<ApolloQueryResult<object>>;
  floatButton?: boolean;
}

const MAX_HEADING = 150;
const EContentItemContentForm: React.FC<IEContentItemContentForm> = ({
  entity,
  onSubmit,
  resource,
  goToContents,
  goToCreate,
  goToCreateItem,
  usedKeywords,
  refetchTranslationSupport,
  floatButton,
}) => {
  const { clobs, language, ...rest } = entity;
  const t = useT();
  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();
  const fsClient = useFsClient();
  const { setTouched } = useEntityFormContext();
  const isNew = useMemo(() => !entity?.id, [entity]);
  const itemId = useMemo(() => entity?.id, [entity]);
  const mainItemId = useMemo(() => entity?.itemId, [entity]);
  const resourceId = useMemo(() => resource?.id, [resource]);
  const libraryId = useMemo(() => resource?.libraryId, [resource]);
  const [atrributeMainIncluded, setAtrributeMainIncluded] = useState<
    IEContentResourceXAttribute[]
  >([]);
  const history = useHistory();
  const { url } = useRouteMatch();
  const { search, pathname } = useLocation();
  const [searchQuery, setSearchQuery] = useState<string>();
  const [contentIndex, setContentIndex] = useState<number | null>(null);
  const [contentFilters, setContentFilters] = useState(null);
  const [updatedCount, setUpdatedCount] = useState<number>(0);

  const isImageAiImprovementEnabled = useMemo(
    () =>
      (resource &&
        resource.attributes &&
        resource.attributes.find(
          x =>
            x.attributeId === attributeTypes.BasicByName.imageAiImprovement?.id,
        )?.isChecked) ||
      false,
    [resource],
  );

  const { goBack: _goBack } = useHistory();
  const queryRes = useQuery(eContentResourceXLanguages, {
    variables: { resourceId },
  });
  const languageIds = useMemo(() => {
    const languageList = get(
      queryRes,
      `data.${getGqlOperationName(eContentResourceXLanguages)}`,
      [],
    );
    let activeLanguages = map(
      filter(languageList, item => item.status === Active.value),
      item => item.languageId,
    );
    if (!isNew) {
      activeLanguages = concat(activeLanguages, language?.id);
    }

    return activeLanguages;
  }, [eContentResourceXLanguages, queryRes, language]);

  const { data: queryContentCountRes, loading: countLoading } = useQuery(
    eContentItemContentsSequence,
    {
      variables: {
        itemId: mainItemId,
        languageId: languageIds,
        status: StatusWithDraft.BasicProps,
      },
      skip: !isNew,
    },
  );

  const contentCount = useMemo(() => {
    const data = get(
      queryContentCountRes,
      `${getGqlOperationName(eContentItemContentsSequence)}`,
      { maxSequence: 0 },
    );
    return data.maxSequence || 0;
  }, [queryContentCountRes]);
  const goBack = useCallback(() => {
    if (isNew) {
      _goBack();
    } else {
      type propState = { fromPage?: string };
      const historyProps: propState = history.location?.state as propState;
      const fromPage = historyProps?.fromPage;
      if (fromPage && fromPage === 'contentview') {
        _goBack();
      } else {
        const _url = url.replace(`/edit/${itemId}`, '');
        history.push(_url);
      }
    }
  }, [history, url, isNew, _goBack]);

  const filterLanguages = useCallback(
    options =>
      isEmpty(languageIds)
        ? options
        : filter(options, option => includes(languageIds, option?.id)),
    [languageIds],
  );

  const cookedAttributes = useMemo(() => {
    let attribute = filter(
      resource.attributes,
      item => item.status === 'ACTIVE',
    );
    if (!isNew) {
      //include deleted element which exist in clobs
      const deletedAttribute = filter(
        resource.attributes,
        item => item.status === 'DELETED',
      );
      const clobResourceAttributeIds = map(
        clobs,
        item => item.resourceAttributeId,
      );
      const _AttributeIncluded = map(
        filter(deletedAttribute, item =>
          clobResourceAttributeIds.includes(item.id),
        ),
        item2 => item2.attributeId,
      );
      let AttributeToBeAdded: number[] = _AttributeIncluded;
      _AttributeIncluded.forEach(item => {
        if (MainSubIds[Number(item)]) {
          AttributeToBeAdded = concat(
            AttributeToBeAdded,
            MainSubIds[Number(item)],
          );
        }
      });
      const addedAttributes = filter(deletedAttribute, item =>
        AttributeToBeAdded.includes(item.attributeId),
      );
      attribute = concat(attribute, addedAttributes);
    }
    return cookEContentResourceXAttributes(attribute);
  }, [resource, isNew, clobs]);

  const validateForm = useCallback(
    values => {
      const minNumberOfDocuments =
        cookedAttributes?.minNumberOfDocuments?.value;
      const minNumberOfImages = cookedAttributes?.minNumberOfImages?.value;
      const minNumberOfVideoFiles =
        cookedAttributes?.minNumberOfVideoFiles?.value;
      const minNumberOfAudioFiles =
        cookedAttributes?.minNumberOfAudioFiles?.value;
      const minNumberOfQuestion = cookedAttributes?.minNumberOfQuestion?.value;

      if (minNumberOfDocuments) {
        const documentsCount = get(
          values,
          ['cookedClobs', 'document', 'length'],
          0,
        );
        if (documentsCount < minNumberOfDocuments) {
          Notifications.error(
            'Error',
            `Attach at least ${minNumberOfDocuments} document${
              minNumberOfDocuments === 1 ? '' : 's'
            }`,

            t,
          );
          return false;
        }
      }

      if (minNumberOfImages) {
        const imagesCount = get(values, ['cookedClobs', 'image', 'length'], 0);
        if (imagesCount < minNumberOfImages) {
          Notifications.error(
            'Error',
            `Attach at least ${minNumberOfImages} image${
              minNumberOfImages === 1 ? '' : 's'
            }`,

            t,
          );
          return false;
        }
      }

      if (minNumberOfVideoFiles) {
        const videoFilesCount = get(
          values,
          ['cookedClobs', 'videoFile', 'length'],
          0,
        );
        if (videoFilesCount < minNumberOfVideoFiles) {
          Notifications.error(
            'Error',
            `Attach at least ${minNumberOfVideoFiles} video file${
              minNumberOfVideoFiles === 1 ? '' : 's'
            }`,

            t,
          );
          return false;
        }
      }

      if (minNumberOfAudioFiles) {
        const audioFilesCount = get(
          values,
          ['cookedClobs', 'audioFile', 'length'],
          0,
        );
        if (audioFilesCount < minNumberOfAudioFiles) {
          Notifications.error(
            'Error',
            `Attach at least ${minNumberOfAudioFiles} audio file${
              minNumberOfAudioFiles === 1 ? '' : 's'
            }`,
            t,
          );
          return false;
        }
      }
      if (minNumberOfQuestion) {
        const contentId = get(values, 'id', 0);
        if (contentId > 0) {
          const questionsCount = get(
            values,
            ['cookedClobs', 'questionTitle', 'length'],
            0,
          );
          if (questionsCount < minNumberOfQuestion) {
            Notifications.error(
              'Error',
              `Attach at least ${minNumberOfQuestion} question${
                minNumberOfQuestion === 1 ? '' : 's'
              }`,
              t,
            );
            return false;
          }
        }
      }

      return true;
    },
    [t, cookedAttributes],
  );

  const _onSubmit = useCallback(
    async values => {
      const {
        id,
        description,
        sequence,
        status,
        itemId,
        languageId,
        cookedClobs: {
          heading,
          subHeading,
          source,
          author,
          topicKeyword,
          text,
          document,
          image,
          videoFile,
          audioFile,
          url,
          question,
        },
      } = values;
      if (!isNew && !validateForm(values)) {
        return false;
      }
      const headingClob = omit(heading, ['attachment', 'attachments']);

      const textClobs = map(
        text,
        (
          {
            text,
            contentClobTypeId,
            textId,
            groupSequence,
            resourceCategoryId,
          },
          index,
        ) => ({
          id: textId,
          content: text,
          groupSequence,
          resourceCategoryId,
          contentClobTypeId,
          resourceAttributeId: cookedAttributes.text?.id,
        }),
      );

      const textKeywords = map(
        text,
        ({ contentKeyword, contentKeywordId }, index) => ({
          id: contentKeywordId,
          content: join(contentKeyword, ','),
          groupSequence: textClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.textKeyword?.id,
        }),
      );

      const documentClobs = map(document, (attachment, index) => ({
        attachments: [
          pick(attachment, [
            'description',
            'fileName',
            'fileId',
            'uploadToken',
          ]),
        ],
        id: attachment?.clobId,
        groupSequence: attachment.groupSequence,
        resourceCategoryId: attachment.resourceCategoryId,
        resourceAttributeId: cookedAttributes.document?.id,
      }));

      const documentKeywords = map(
        document,
        ({ contentKeyword, contentKeywordId }, index) => ({
          id: contentKeywordId,
          content: join(contentKeyword, ','),
          groupSequence: documentClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.documentKeyword?.id,
        }),
      );

      const imageClobs = map(image, (attachment, index) => ({
        attachments: [
          pick(attachment, [
            'description',
            'fileName',
            'fileId',
            'uploadToken',
          ]),
        ],
        id: attachment?.clobId,
        groupSequence: attachment.groupSequence,
        resourceCategoryId: attachment.resourceCategoryId,
        resourceAttributeId: cookedAttributes.image?.id,
      }));

      const imageKeywords = map(
        image,
        ({ contentKeyword, contentKeywordId }, index) => ({
          id: contentKeywordId,
          content: join(contentKeyword, ','),
          groupSequence: imageClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.imageKeyword?.id,
        }),
      );

      const videoClobs = map(videoFile, (attachment, index) => ({
        attachments: [
          pick(attachment, [
            'description',
            'fileName',
            'fileId',
            'uploadToken',
          ]),
        ],
        id: attachment?.clobId,
        groupSequence: attachment.groupSequence,
        resourceCategoryId: attachment.resourceCategoryId,
        resourceAttributeId: cookedAttributes.videoFile?.id,
      }));

      const videoFileLengthClobs = map(
        videoFile,
        ({ duration, videoFileLengthId }, index) => ({
          content: String(duration),
          id: videoFileLengthId,
          groupSequence: videoClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.videoFileLength?.id,
        }),
      );

      const videoFileKeywords = map(
        videoFile,
        ({ contentKeyword, contentKeywordId }, index) => ({
          id: contentKeywordId,
          content: join(contentKeyword, ','),
          groupSequence: videoClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.videoFileKeyword?.id,
        }),
      );

      const audioClobs = map(audioFile, (attachment, index) => ({
        attachments: [
          pick(attachment, [
            'description',
            'fileName',
            'fileId',
            'uploadToken',
          ]),
        ],
        id: attachment?.clobId,
        groupSequence: attachment.groupSequence,
        resourceCategoryId: attachment.resourceCategoryId,
        resourceAttributeId: cookedAttributes.audioFile?.id,
      }));

      const audioFileLengthClobs = map(
        audioFile,
        ({ duration, audioFileLengthId }, index) => ({
          content: String(duration),
          id: audioFileLengthId,
          groupSequence: audioClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.audioFileLength?.id,
        }),
      );

      const audioFileKeywords = map(
        audioFile,
        ({ contentKeyword, contentKeywordId }, index) => ({
          id: contentKeywordId,
          content: join(contentKeyword, ','),
          groupSequence: audioClobs[index]?.groupSequence || index + ONE,
          resourceAttributeId: cookedAttributes.audioFileKeyword?.id,
        }),
      );

      const urlClobs = map(
        url,
        ({ url, urlId, groupSequence, resourceCategoryId }, index) => ({
          id: urlId,
          content: url,
          groupSequence,
          resourceCategoryId,
          resourceAttributeId: cookedAttributes.url?.id,
        }),
      );

      const urlNames = map(
        url,
        ({ urlName, urlNameId, groupSequence }, index) => ({
          id: urlNameId,
          content: urlName,
          groupSequence,
          resourceAttributeId: cookedAttributes.urlName?.id,
        }),
      );

      const urlCaptions = map(
        url,
        ({ urlCaption, urlCaptionId, groupSequence }, index) => ({
          id: urlCaptionId,
          content: urlCaption,
          groupSequence,
          resourceAttributeId: cookedAttributes.urlCaption?.id,
        }),
      );

      const urlKeywords = map(url, ({ urlKeyword, urlKeywordId }, index) => ({
        id: urlKeywordId,
        content: join(urlKeyword, ','),
        groupSequence: urlClobs[index]?.groupSequence || index + ONE,
        resourceAttributeId: cookedAttributes.urlKeyword?.id,
      }));
      const clobs = [
        {
          content: join(topicKeyword?.content, ','),
          resourceAttributeId: cookedAttributes.topicKeyword?.id,
        },
        {
          ...source,
          resourceAttributeId: cookedAttributes.source?.id,
        },
        {
          ...author,
          resourceAttributeId: cookedAttributes.author?.id,
        },
        {
          ...headingClob,
          resourceAttributeId: cookedAttributes.heading?.id,
        },
        !!resource?.contentSubHeadingLabel
          ? {
              ...subHeading,
              resourceAttributeId: cookedAttributes.subHeading?.id,
            }
          : {},
        ...textClobs,
        ...textKeywords,
        ...documentClobs,
        ...documentKeywords,
        ...imageClobs,
        ...imageKeywords,
        ...videoClobs,
        ...videoFileLengthClobs,
        ...videoFileKeywords,
        ...audioClobs,
        ...audioFileLengthClobs,
        ...audioFileKeywords,
        ...urlClobs,
        ...urlNames,
        ...urlCaptions,
        ...urlKeywords,
      ];

      if (isNew) {
        const _atrributeMainIncluded = map(
          clobs,
          item => item.resourceAttributeId,
        );
        const atrributeNotIncluded = filter(
          atrributeMainIncluded,
          item => !_atrributeMainIncluded.includes(item.id),
        );
        atrributeNotIncluded.forEach(item => {
          clobs.push({
            id: null,
            content: null,
            groupSequence: null,
            resourceAttributeId: item.id,
            attachments: [],
          });
        });
      }
      if (audioClobs.length > cookedAttributes?.maxNumberOfAudioFiles?.value) {
        Notifications.error(
          'Error',
          `No more than ${cookedAttributes?.maxNumberOfAudioFiles?.value} file${
            cookedAttributes?.maxNumberOfAudioFiles?.value > 1 ? 's' : ''
          } allowed`,
          t,
        );
        return false;
      }
      try {
        await onSubmit({
          id,
          itemId,
          description,
          sequence,
          status,
          resourceId: resource.id,
          languageId,
          clobs: filter(clobs, item => !!item?.resourceAttributeId),
        } as IEContentContentEntity);
      } catch (e) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        e && e.message && Notifications.error(t('Error'), e.message, t);
        return false;
      }

      localStorage.setItem('organisationId', languageId);
      if (isNew) {
        Notifications.success(t('Created Successfully'), '', t);
      } else {
        Notifications.success(t('Updated Successfully'), '', t);
      }
      return true;
    },
    [
      t,
      isNew,
      onSubmit,
      cookedAttributes,
      resource,
      validateForm,
      atrributeMainIncluded,
    ],
  );

  const _entity = useMemo(() => {
    if (isNew) {
      return {
        ...entity,
        status: Active.value,
        sequence: countLoading
          ? ''
          : round(
              contentCount === 0
                ? contentCount + ONE
                : contentCount + POINT_ONE,
              TWO,
            ),
        languageId: parseInt(localStorage.getItem('organisationId') as string),
      };
    }

    return {
      ...rest,
      sequence: entity?.sequence || '',
      languageId: language?.id,
      cookedClobs: cookEContentClobs(clobs, resource.attributes),
    };
  }, [isNew, entity, resource, contentCount, countLoading]);

  useEffect(() => {
    setTouched({});
  }, [_entity]);

  const isKeywordRequired = useMemo(
    () => cookedAttributes?.contentKeyword?.rule === MANDATORY.value,
    [cookedAttributes],
  );

  const { maxSize } = useStorageSettings(E_CONTENT_CONTENT_FILE, tenantId);

  const uploadFile = useCallback(
    async (value: string) =>
      // eslint-disable-next-line react-hooks/rules-of-hooks
      await useEditorImageProcess({
        value,
        fileCategory: E_CONTENT_CONTENT_FILE,
        fsClient,
        tenantId,
        organisationGroupId,
        t,
        maxSize,
        isResizable: true,
      }),
    [fsClient, tenantId, organisationGroupId, t, maxSize],
  );

  const handleSubmit = useCallback(
    async values => {
      const texts = values.cookedClobs.text;
      if (texts) {
        const delimiter = 'DELIMITER_SEPERATOR';
        const concatenatedText = texts.map(t => t.text).join(delimiter);
        const uploadedConcatenatedText = (await uploadFile(
          concatenatedText,
        )) as string;
        const uploadedTextsArray = uploadedConcatenatedText.split(delimiter);
        const updatedTexts = texts.map((t, index) => ({
          ...t,
          text: uploadedTextsArray[index],
        }));
        values.cookedClobs.text = updatedTexts;
      }
      const res = await _onSubmit(values);
      refetchTranslationSupport && refetchTranslationSupport();
      setUpdatedCount(prev => prev + 1);
      return res;
    },
    [_onSubmit, refetchTranslationSupport, uploadFile],
  );

  const handleCreateAndAddNewContent = useCallback(
    async values => {
      const res = await _onSubmit(values);
      res && goToCreate();
    },
    [_onSubmit, goToCreate],
  );

  const handleCreateAndAddNewItem = useCallback(
    async values => {
      const res = await _onSubmit(values);
      res && goToCreateItem();
    },
    [_onSubmit, goToCreateItem],
  );

  const submitActions = useMemo(
    () => [
      {
        text: t('Create and Add New Content'),
        onClick: handleCreateAndAddNewContent,
        validate: true,
      },
      {
        text: t('Create and Add New Item'),
        onClick: handleCreateAndAddNewItem,
        validate: true,
      },
    ],
    [t, handleCreateAndAddNewItem, handleCreateAndAddNewContent],
  );

  const isFrameworkPanelVisible = useMemo(
    () =>
      [
        !!cookedAttributes?.author,
        !!cookedAttributes?.source,
        !!cookedAttributes?.topicKeyword,
      ].some(x => x),

    [cookedAttributes],
  );

  const contentPanelColumns = useMemo(
    () =>
      isFrameworkPanelVisible
        ? WITH_FRAMEWORK
        : DEFAULT_WRAPPER_CLASS_NAMES[ONE],
    [isFrameworkPanelVisible],
  );

  const isTransformEnabled = useMemo(
    () =>
      resource.attributes &&
      !!resource.attributes.find(
        x => x.attributeId === attributeTypes.BasicByName.transform?.id,
      )?.isChecked,
    [resource],
  );

  const { isEditSessionActive } = useContext(EditSessionContext);

  const showTextClasses = useMemo(
    () =>
      isEditSessionActive ||
      !!cookEContentClobs(clobs, resource.attributes)?.text,
    [isEditSessionActive, clobs, resource.attributes],
  );

  const showDocClasses = useMemo(
    () =>
      isEditSessionActive ||
      !!cookEContentClobs(clobs, resource.attributes)?.document,
    [isEditSessionActive, clobs, resource.attributes],
  );

  const showImgClasses = useMemo(
    () =>
      isEditSessionActive ||
      !!cookEContentClobs(clobs, resource.attributes)?.image,
    [isEditSessionActive, clobs, resource.attributes],
  );

  const showVideoClasses = useMemo(
    () =>
      isEditSessionActive ||
      !!cookEContentClobs(clobs, resource.attributes)?.videoFile,
    [isEditSessionActive, clobs, resource.attributes],
  );

  const showAudioClasses = useMemo(
    () =>
      isEditSessionActive ||
      !!cookEContentClobs(clobs, resource.attributes)?.audioFile,
    [isEditSessionActive, clobs, resource.attributes],
  );

  const showUrlClasses = useMemo(
    () =>
      isEditSessionActive ||
      !!cookEContentClobs(clobs, resource.attributes)?.url,
    [isEditSessionActive, clobs, resource.attributes],
  );

  const isTextToAudioEnabled = useMemo(
    () =>
      resource &&
      resource.attributes &&
      resource.attributes.find(
        x => x.attributeId === attributeTypes.BasicByName.textToAudio?.id,
      )?.isChecked,
    [resource],
  );

  const createGroupedObject = (
    documents: IEContentClob[],
    videos: IEContentClob[],
    images: IEContentClob[],
    audio: IEContentClob[],
    urls: IEContentClob[],
  ) => {
    const result = {};

    const groupData = {
      doc: groupBy(documents, 'groupSequence'),
      vid: groupBy(videos, 'groupSequence'),
      img: groupBy(images, 'groupSequence'),
      aud: groupBy(audio, 'groupSequence'),
      url: groupBy(urls, 'groupSequence'),
    };

    forEach(groupData, (group, prefix) => {
      forEach(group, (items, groupSequence) => {
        forEach(items, item => {
          result[`${prefix}${groupSequence}`] = item;
        });
      });
    });

    return result;
  };

  const clobsPreviewData = useMemo(() => {
    if (isNew) return undefined;
    const { cookedClobs } = _entity as any;
    const { image, videoFile, url, document, audioFile } = cookedClobs;
    return createGroupedObject(document, videoFile, image, audioFile, url);
  }, [_entity, isNew]);

  const isContentCategoryEnabled = useMemo(
    () =>
      (cookedAttributes &&
        cookedAttributes.contentCategory &&
        cookedAttributes.contentCategory.isChecked) ||
      false,
    [cookedAttributes],
  );

  const resourceCategoriesOptions = useMemo(
    () =>
      (cookedAttributes &&
        cookedAttributes.contentCategory &&
        cookedAttributes.contentCategory.categories) ||
      [],
    [cookedAttributes],
  );

  const detailsTab = useMemo(
    () => (
      <EntityFormFieldSet className={styles.topFlex}>
        <div className={classnames(styles.content, contentPanelColumns)}>
          <h6>{t('Details')}</h6>
          <EntityFormFieldSet
            className={classnames('no-margin-left', styles.wrapper)}
          >
            <LanguageSelectorField
              required
              columns={
                isFrameworkPanelVisible
                  ? THREE_FIELDS_IN_ROW
                  : FOUR_FIELDS_IN_ROW
              }
              defaultSelected={
                resource?.languageIds && resource.languageIds.length === 1
                  ? resource.languageIds[0]
                  : 0
              }
              filterOptions={filterLanguages}
            />
            <EntityNameField
              columns={
                isFrameworkPanelVisible
                  ? THREE_FIELDS_IN_ROW
                  : FOUR_FIELDS_IN_ROW
              }
              label={t(resource.contentHeadingLabel)}
              maxLength={MAX_HEADING}
              name="cookedClobs.heading.content"
            />
            {resource?.contentSubHeadingLabel ? (
              <EntityNameField
                hasPlaceholderOnEmptyValue
                columns={
                  isFrameworkPanelVisible
                    ? THREE_FIELDS_IN_ROW
                    : FOUR_FIELDS_IN_ROW
                }
                emptyPlaceHolder={t('None')}
                label={t(resource.contentSubHeadingLabel)}
                maxLength={MAX_HEADING}
                name="cookedClobs.subHeading.content"
                required={false}
              />
            ) : null}
            <NumberField
              required
              columns={
                isFrameworkPanelVisible
                  ? THREE_FIELDS_IN_ROW
                  : FOUR_FIELDS_IN_ROW
              }
              label={t('Sequence')}
              min={ONE}
              name="sequence"
              precision={2}
              step={0.01}
            />

            <StatusWithDraftField
              columns={
                isFrameworkPanelVisible
                  ? THREE_FIELDS_IN_ROW
                  : FOUR_FIELDS_IN_ROW
              }
            />

            <TextAreaField
              autoHeight
              columns={1}
              label={t('Description')}
              maxLength={4000}
              name="description"
            />
          </EntityFormFieldSet>
        </div>

        {isFrameworkPanelVisible ? (
          <div
            className={classnames(
              styles.framework,
              'col-lg-4 col-md-6 col-sm-12 col-xs-12',
            )}
          >
            <h6>{t('Framework')}</h6>

            <EntityFormFieldSet
              className={classnames(styles.wrapper, styles.mobileFrameworkDiv)}
            >
              {isVisible(isNew, cookedAttributes?.author) ? (
                <EntityNameField
                  hasPlaceholderOnEmptyValue
                  columns={1}
                  emptyPlaceHolder={t('None')}
                  label={t('Author')}
                  name="cookedClobs.author.content"
                  required={cookedAttributes.author.rule === MANDATORY.value}
                />
              ) : null}
              {isVisible(isNew, cookedAttributes?.source) ? (
                <EntityNameField
                  hasPlaceholderOnEmptyValue
                  columns={1}
                  emptyPlaceHolder={t('None')}
                  label={t('Source')}
                  maxLength={254}
                  name="cookedClobs.source.content"
                  required={cookedAttributes.source.rule === MANDATORY.value}
                />
              ) : null}
              {isVisible(isNew, cookedAttributes?.topicKeyword) ? (
                <TagInputField
                  columns={1}
                  label={t('Topic Keyword')}
                  maxTagLength={cookedAttributes.topicKeyword.value}
                  name="cookedClobs.topicKeyword.content"
                  required={
                    cookedAttributes.topicKeyword.rule === MANDATORY.value
                  }
                  suggestions={usedKeywords}
                />
              ) : null}
            </EntityFormFieldSet>
          </div>
        ) : null}
      </EntityFormFieldSet>
    ),
    [
      contentFilters,
      contentIndex,
      contentPanelColumns,
      cookedAttributes,
      filterLanguages,
      isFrameworkPanelVisible,
      isNew,
      itemId,
      mainItemId,
      resource,
      searchQuery,
      t,
      updatedCount,
      usedKeywords,
    ],
  );

  const [selectedTabId, setSelectedTabId] = useState<number | string>('all');

  const handleCategoryTabChange = useCallback(selectedTab => {
    setSelectedTabId(selectedTab.id);
  }, []);

  const [hasItems, setHasItems] = useState({
    text: false,
    document: false,
    videoFile: false,
    image: false,
    audioFile: false,
    url: false,
  });
  const [usedCategories, setUsedCategories] = useState<number[]>([]);

  const hasItemsFn = useCallback(
    (name: string, currentItems: any[]) => {
      const categoryIds = currentItems.map(x => x.resourceCategoryId);
      setUsedCategories(prev => {
        const newCategories = difference(categoryIds, prev);
        return uniq([...prev, ...newCategories]);
      });
      let value = false;
      if (selectedTabId === 'all') {
        value = true;
      } else if (currentItems.length) {
        if (selectedTabId === 'no-category') {
          value = currentItems.some(
            x => !x.resourceCategoryId || x.resourceCategoryId === '',
          );
        } else {
          value = currentItems.some(
            x => x.resourceCategoryId === selectedTabId,
          );
        }
      }

      setHasItems(prev => ({ ...prev, [name]: value }));
    },
    [selectedTabId],
  );

  const contentTab = useMemo(
    () => (
      <>
        {isVisible(isNew, cookedAttributes?.text) ? (
          <EntityFormFieldSet
            className={classnames(styles.wrapper, 'no-margin-left', {
              hidden: !showTextClasses || !hasItems.text,
            })}
          >
            <TextSectionArrayField
              clobsPreviewData={clobsPreviewData}
              cookedAttributes={cookedAttributes}
              hasItems={hasItemsFn}
              hasKeyword={!!cookedAttributes?.contentKeyword}
              hasRenderedPreviewRichText={
                cookedAttributes?.previewRenderingRichText?.isChecked
              }
              hasRenderedPreviewSimpleText={
                cookedAttributes?.previewRenderingSimpleText?.isChecked
              }
              isContentCategoryEnabled={isContentCategoryEnabled}
              isKeywordRequired={isKeywordRequired}
              isTransformEnabled={isTransformEnabled}
              maxNumberOfTextBoxes={
                cookedAttributes?.maxNumberOfTextBoxes?.value
              }
              maxTagLength={cookedAttributes.contentKeyword?.value}
              minNumberOfTextBoxes={
                cookedAttributes?.minNumberOfTextBoxes?.value
              }
              resourceCategoriesOptions={resourceCategoriesOptions}
              selectedTabId={selectedTabId}
              suggestions={usedKeywords}
              textBoxesType={cookedAttributes?.text?.value}
              textCharactersNumber={
                cookedAttributes?.textCharactersNumber?.value
              }
              textHeight={cookedAttributes?.textHeight?.value}
            />
          </EntityFormFieldSet>
        ) : null}

        {isVisible(isNew, cookedAttributes?.document) ? (
          <EntityFormFieldSet
            className={classnames({
              [styles.wrapper]: showDocClasses,
              'no-margin-left mt-10': showDocClasses,
              hidden: !hasItems.document,
            })}
          >
            <DocumentsSectionArrayField
              documentCaptionRule={cookedAttributes?.documentCaption?.rule}
              hasCaption={!!cookedAttributes?.documentCaption}
              hasItems={hasItemsFn}
              hasKeyword={!!cookedAttributes?.contentKeyword}
              isContentCategoryEnabled={isContentCategoryEnabled}
              isKeywordRequired={isKeywordRequired}
              maxNumberOfDocuments={
                cookedAttributes?.maxNumberOfDocuments?.value
              }
              maxSizeOfEachDocument={
                cookedAttributes?.maxSizeOfEachDocument?.value
              }
              maxTagLength={cookedAttributes.contentKeyword?.value}
              minNumberOfDocuments={
                cookedAttributes?.minNumberOfDocuments?.value
              }
              resourceCategoriesOptions={resourceCategoriesOptions}
              selectedTabId={selectedTabId}
              suggestions={usedKeywords}
            />
          </EntityFormFieldSet>
        ) : null}

        {isVisible(isNew, cookedAttributes?.image) ? (
          <EntityFormFieldSet
            className={classnames({
              [styles.wrapper]: showImgClasses,
              'no-margin-left mt-10': showImgClasses,
              hidden: !hasItems.image,
            })}
          >
            <ImagesSectionArrayField
              hasCaption={!!cookedAttributes?.imageCaption}
              hasItems={hasItemsFn}
              hasKeyword={!!cookedAttributes?.contentKeyword}
              imageCaptionRule={cookedAttributes?.imageCaption?.rule}
              isContentCategoryEnabled={isContentCategoryEnabled}
              isImageAiImprovementEnabled={isImageAiImprovementEnabled}
              isKeywordRequired={isKeywordRequired}
              maxNumberOfImages={cookedAttributes?.maxNumberOfImages?.value}
              maxSizeOfEachImage={cookedAttributes?.maxSizeOfEachImage?.value}
              maxTagLength={cookedAttributes.contentKeyword?.value}
              minNumberOfImages={cookedAttributes?.minNumberOfImages?.value}
              resourceCategoriesOptions={resourceCategoriesOptions}
              selectedTabId={selectedTabId}
              suggestions={usedKeywords}
            />
          </EntityFormFieldSet>
        ) : null}

        {isVisible(isNew, cookedAttributes?.videoFile) ? (
          <EntityFormFieldSet
            className={classnames({
              [styles.wrapper]: showVideoClasses,
              'no-margin-left mt-10': showVideoClasses,
              hidden: !hasItems.videoFile,
            })}
          >
            <VideoFilesSectionArrayField
              hasCaption={!!cookedAttributes?.videoFileCaption}
              hasItems={hasItemsFn}
              hasKeyword={!!cookedAttributes?.contentKeyword}
              isContentCategoryEnabled={isContentCategoryEnabled}
              isKeywordRequired={isKeywordRequired}
              maxNumberOfVideoFiles={
                cookedAttributes?.maxNumberOfVideoFiles?.value
              }
              maxSizeOfEachVideoFile={
                cookedAttributes?.maxSizeOfEachVideoFile?.value
              }
              maxTagLength={cookedAttributes.contentKeyword?.value}
              minNumberOfVideoFiles={
                cookedAttributes?.minNumberOfVideoFiles?.value
              }
              resourceCategoriesOptions={resourceCategoriesOptions}
              selectedTabId={selectedTabId}
              suggestions={usedKeywords}
              videoFileCaptionRule={cookedAttributes?.videoFileCaption?.rule}
            />
          </EntityFormFieldSet>
        ) : null}

        {isVisible(isNew, cookedAttributes?.audioFile) ? (
          <EntityFormFieldSet
            className={classnames({
              [styles.wrapper]: showAudioClasses,
              'no-margin-left mt-10': showAudioClasses,
              hidden: !hasItems.audioFile,
            })}
          >
            <AudioFilesSectionArrayField
              audioFileCaptionRule={cookedAttributes?.audioFileCaption?.rule}
              audioValidation={cookedAttributes?.audioValidation?.isChecked}
              audioValidationParameters={{
                pitch: cookedAttributes?.pitch?.value,
                frequency: cookedAttributes?.frequency?.value,
                noise: cookedAttributes?.noise?.value,
                loudness: cookedAttributes?.loudness?.value,
              }}
              hasCaption={!!cookedAttributes?.audioFileCaption}
              hasItems={hasItemsFn}
              hasKeyword={!!cookedAttributes?.contentKeyword}
              isContentCategoryEnabled={isContentCategoryEnabled}
              isKeywordRequired={isKeywordRequired}
              isTextToAudioEnabled={isTextToAudioEnabled}
              maxNumberOfAudioFiles={
                cookedAttributes?.maxNumberOfAudioFiles?.value
              }
              maxSizeOfEachAudioFile={
                cookedAttributes?.maxSizeOfEachAudioFile?.value
              }
              maxTagLength={cookedAttributes.contentKeyword?.value}
              minNumberOfAudioFiles={
                cookedAttributes?.minNumberOfAudioFiles?.value
              }
              resourceCategoriesOptions={resourceCategoriesOptions}
              selectedTabId={selectedTabId}
              suggestions={usedKeywords}
            />
          </EntityFormFieldSet>
        ) : null}

        {isVisible(isNew, cookedAttributes?.url) ? (
          <EntityFormFieldSet
            className={classnames(styles.wrapper, 'no-margin-left  mt-10', {
              hidden: !showUrlClasses || !hasItems.url,
            })}
          >
            <UrlSectionArrayField
              hasCaption={!!cookedAttributes?.urlCaption}
              hasItems={hasItemsFn}
              hasKeyword={!!cookedAttributes?.contentKeyword}
              isContentCategoryEnabled={isContentCategoryEnabled}
              isKeywordRequired={isKeywordRequired}
              maxNumberOfUrls={cookedAttributes?.maxNumberOfUrls?.value}
              maxTagLength={cookedAttributes.contentKeyword?.value}
              minNumberOfUrls={cookedAttributes?.minNumberOfUrls?.value}
              resourceCategoriesOptions={resourceCategoriesOptions}
              selectedTabId={selectedTabId}
              suggestions={usedKeywords}
              urlCaptionRule={cookedAttributes?.urlCaption?.rule}
            />
          </EntityFormFieldSet>
        ) : null}
      </>
    ),
    [
      isImageAiImprovementEnabled,
      isContentCategoryEnabled,
      resourceCategoriesOptions,
      isNew,
      usedKeywords,
      cookedAttributes,
      isKeywordRequired,
      showImgClasses,
      showAudioClasses,
      showDocClasses,
      showTextClasses,
      showUrlClasses,
      showVideoClasses,
      isTransformEnabled,
      isTextToAudioEnabled,
      selectedTabId,
      hasItems,
      hasItemsFn,
    ],
  );

  const contentTabWithCatgeories = useMemo(
    () => (
      <>
        {isContentCategoryEnabled && resourceCategoriesOptions.length && (
          <Tabs
            horizontal
            hasRoutes={false}
            saveTabs={false}
            onChange={handleCategoryTabChange}
          >
            <Tabs.Tab key="all" default route="all" title={t('All')}>
              {contentTab}
            </Tabs.Tab>
            <Tabs.Tab
              key="no-category"
              route="no-category"
              title={t('No Category')}
            >
              {contentTab}
            </Tabs.Tab>
            {resourceCategoriesOptions.map(
              x =>
                usedCategories.includes(x.id) && (
                  <Tabs.Tab key={x.id} route={x.id} title={t(x.name)}>
                    {contentTab}
                  </Tabs.Tab>
                ),
            )}
          </Tabs>
        )}
      </>
    ),
    [
      usedCategories,
      contentTab,
      t,
      handleCategoryTabChange,
      isContentCategoryEnabled,
      resourceCategoriesOptions,
    ],
  );

  const questionTab = useMemo(
    () => (
      <QuestionSectionArrayField
        hasRenderedPreview
        clobsPreviewData={clobsPreviewData}
        contentId={entity?.id}
        hasKeyword={!!cookedAttributes?.contentKeyword}
        isKeywordRequired={isKeywordRequired}
        maxNumberOfQuestion={cookedAttributes?.maxNumberOfQuestion?.value}
        maxTagLength={cookedAttributes.contentKeyword?.value}
        minNumberOfQuestion={cookedAttributes?.minNumberOfQuestion?.value}
        resource={resource}
        suggestions={usedKeywords}
      />
    ),
    [
      entity,
      resource,
      cookedAttributes,
      isKeywordRequired,
      usedKeywords,
      clobsPreviewData,
    ],
  );
  const hasContent = useMemo(
    () =>
      isVisible(isNew, cookedAttributes?.text) ||
      isVisible(isNew, cookedAttributes?.document) ||
      isVisible(isNew, cookedAttributes?.image) ||
      isVisible(isNew, cookedAttributes?.videoFile) ||
      isVisible(isNew, cookedAttributes?.audioFile) ||
      isVisible(isNew, cookedAttributes?.url),
    [isNew, cookedAttributes],
  );
  const hasQuestion = useMemo(
    () => isVisible(isNew, cookedAttributes?.question),
    [isNew, cookedAttributes],
  );

  useEffect(() => {
    const params = new URLSearchParams(search);
    if (params.has('searchQuery')) {
      setSearchQuery(params.get('searchQuery') as string);
    }
    if (params.has('contentIndex')) {
      setContentIndex(Number(params.get('contentIndex')));
    }
    if (params.has('contentFilters')) {
      const cFilters =
        params.get('contentFilters')?.split('/')[0] ||
        params.get('contentFilters');
      if (!isEmpty(cFilters)) setContentFilters(JSON.parse(cFilters as string));
    }

    // Check if current route matches any available tab
    const isOnValidTab =
      (pathname.endsWith('content') && hasContent) ||
      (pathname.endsWith('questions') && hasQuestion) ||
      pathname.endsWith('audit-log');

    if (isOnValidTab) {
      history.replace(pathname);
    } else {
      // Determine the first available tab to redirect to
      let defaultTab = 'audit-log'; // audit-log is always available as fallback
      if (hasContent) {
        defaultTab = 'content';
      } else if (hasQuestion) {
        defaultTab = 'questions';
      }

      history.replace(
        `${pathname.replace(
          /\/(contents|questions|audit-log)$/,
          '',
        )}/${defaultTab}`,
      );
    }
  }, [search, history, pathname, hasContent, hasQuestion]);

  const leftBottomSection = (
    <>
      {entity.updatedAt && entity.updatedByPerson && (
        <p>
          {t('Last updated by')}&nbsp;
          {`${entity.updatedByPerson.firstName} ${entity.updatedByPerson.lastName}`}
          &nbsp;on&nbsp;
          {format(entity.updatedAt, ValidatedAt)}
        </p>
      )}
    </>
  );

  return (
    <Tabs additionalClasses={['mb-0']} className={styles.tabContainer}>
      <Tabs.Tab default route="details" title={t('Details')}>
        <EntityForm
          hasLeavePrompt
          pullLeft
          entity={_entity}
          floatButton={floatButton}
          hasCreateMessage={false}
          hasUpdateMessage={false}
          hasValidateOnBlur={false}
          hasValidateOnChange={false}
          leftBottomSection={leftBottomSection}
          submitActions={isNew ? submitActions : undefined}
          submitClass="no-padding-left no-padding-right"
          submitContainerClass={classnames('no-margin-left no-margin-right', {
            [styles.mb60]: isNew,
          })}
          updateLabel={t('Update')}
          validateOnMount={false}
          onCancel={isNew ? goBack : undefined}
          onGoBack={isNew ? undefined : goBack}
          onSubmit={handleSubmit}
        >
          {detailsTab}
        </EntityForm>
      </Tabs.Tab>
      {!isNew && hasContent ? (
        <Tabs.Tab default={hasContent} route="content" title={t('Content')}>
          <EntityForm
            hasLeavePrompt
            pullLeft
            entity={_entity}
            floatButton={floatButton}
            hasCreateMessage={false}
            hasUpdateMessage={false}
            hasValidateOnBlur={false}
            hasValidateOnChange={false}
            leftBottomSection={leftBottomSection}
            submitActions={isNew ? submitActions : undefined}
            submitClass="no-padding-left no-padding-right"
            submitContainerClass={classnames('no-margin-left no-margin-right', {
              [styles.mb60]: isNew,
            })}
            updateLabel={t('Update')}
            validateOnMount={false}
            onCancel={isNew ? goBack : undefined}
            onGoBack={isNew ? undefined : goBack}
            onSubmit={handleSubmit}
          >
            <div className="col-lg-12 no-padding">
              <h6>{t('Content')}</h6>
              {isContentCategoryEnabled && resourceCategoriesOptions.length
                ? contentTabWithCatgeories
                : contentTab}
            </div>
          </EntityForm>
        </Tabs.Tab>
      ) : null}
      {!isNew && hasQuestion ? (
        <Tabs.Tab
          default={!hasContent && hasQuestion}
          route="questions"
          title={t('Questions')}
        >
          <EntityForm
            hasLeavePrompt
            isReadOnly
            pullLeft
            entity={_entity}
            floatButton={floatButton}
            hasCreateMessage={false}
            hasUpdateMessage={false}
            hasValidateOnBlur={false}
            hasValidateOnChange={false}
            // leftBottomSection={leftBottomSection}
            submitActions={isNew ? submitActions : undefined}
            submitClass="no-padding-left no-padding-right"
            submitContainerClass={classnames('no-margin-left no-margin-right', {
              [styles.mb60]: isNew,
            })}
            updateLabel={t('Update')}
            validateOnMount={false}
            onCancel={isNew ? goBack : undefined}
            onGoBack={isNew ? undefined : goBack}
            onSubmit={handleSubmit}
          >
            {questionTab}
          </EntityForm>
        </Tabs.Tab>
      ) : null}
      {!isNew ? (
        <Tabs.Tab
          default={!hasContent && !hasQuestion}
          route="audit-log"
          title={t('Audit Log')}
        >
          <EntityForm
            hasLeavePrompt
            isReadOnly
            pullLeft
            entity={_entity}
            floatButton={floatButton}
            hasCreateMessage={false}
            hasUpdateMessage={false}
            hasValidateOnBlur={false}
            hasValidateOnChange={false}
            // leftBottomSection={leftBottomSection}
            submitActions={isNew ? submitActions : undefined}
            submitClass="no-padding-left no-padding-right"
            submitContainerClass={classnames('no-margin-left no-margin-right', {
              [styles.mb60]: isNew,
            })}
            updateLabel={t('Update')}
            validateOnMount={false}
            onCancel={isNew ? goBack : undefined}
            onGoBack={isNew ? undefined : goBack}
            onSubmit={handleSubmit}
          >
            <EContentItemLogTab contentId={itemId} />
          </EntityForm>
        </Tabs.Tab>
      ) : null}
    </Tabs>
  );
};

export default EContentItemContentForm;

function isVisible(isNew, attribute?: IEContentResourceXAttribute) {
  if (!attribute) {
    return false;
  }

  const { status } = attribute || {};
  return (isNew && status === StatusWithDraft.Active.value) || !isNew;
}

export const cookEContentClobs = (
  clobs: IEContentClob[] = [],
  eContentResourceXAttributes: IEContentResourceXAttribute[] = [],
): Record<string, IEContentClob> => {
  const hash = keyBy(eContentResourceXAttributes, 'id');
  const multipleList: string[] = [];
  clobs = orderBy(clobs, ['groupSequence', 'id'], ['asc', 'asc']);
  map(clobs, (item, index) => {
    const attributeId = hash[item.resourceAttributeId]?.attributeId;
    return attributeId === questionMultipleType.id && item.content
      ? multipleList.push(item.content)
      : null;
  });
  const getIndexByNameAndGroupSequence = (
    array: IEContentClob[],
    attributeId: number,
    id: number,
  ): number => {
    const nameGroup = array.filter(
      item => hash[item.resourceAttributeId]?.attributeId === attributeId,
    );
    for (let i = ZERO; i < nameGroup.length; i++) {
      if (nameGroup[i].id === id) {
        return i;
      }
    }
    return ZERO;
  };

  return reduce(
    clobs,
    (acc, clob) => {
      const attributeId = hash[clob.resourceAttributeId]?.attributeId;
      const attributeType = attributeTypes.BasicById[attributeId]?.name;
      const groupIndex = getIndexByNameAndGroupSequence(
        clobs,
        attributeId,
        clob?.id || 0,
      );
      switch (true) {
        case includes(
          ['document', 'image', 'videoFile', 'audioFile'],
          attributeType,
        ): {
          if (clob.attachments && clob.attachments[0]) {
            const cookedClob = {
              ...clob.attachments[0],
              groupSequence: clob.groupSequence,
              resourceCategoryId: clob.resourceCategoryId,
              clobId: clob?.id,
              contentKeyword: get(
                acc,
                [attributeType, groupIndex, 'contentKeyword'],
                [],
              ),
            };
            set(acc, [attributeType, groupIndex], cookedClob);
          }
          break;
        }
        case includes(['documentKeyword'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['document', groupIndex, 'contentKeyword'],
              split(clob.content, ','),
            );
            set(acc, ['document', groupIndex, 'contentKeywordId'], clob?.id);
          }
          break;
        }
        case includes(['imageKeyword'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['image', groupIndex, 'contentKeyword'],
              split(clob.content, ','),
            );
            set(acc, ['image', groupIndex, 'contentKeywordId'], clob?.id);
          }
          break;
        }
        case includes(['audioFileKeyword'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['audioFile', groupIndex, 'contentKeyword'],
              split(clob.content, ','),
            );
            set(acc, ['audioFile', groupIndex, 'contentKeywordId'], clob?.id);
          }
          break;
        }
        case includes(['audioFileLength'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['audioFile', groupIndex, 'duration'],
              Number(clob?.content),
            );
            set(acc, ['audioFile', groupIndex, 'audioFileLengthId'], clob?.id);
          }
          break;
        }
        case includes(['videoFileKeyword'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['videoFile', groupIndex, 'contentKeyword'],
              split(clob.content, ','),
            );
            set(acc, ['videoFile', groupIndex, 'contentKeywordId'], clob?.id);
          }
          break;
        }
        case includes(['videoFileLength'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['videoFile', groupIndex, 'duration'],
              Number(clob?.content),
            );
            set(acc, ['videoFile', groupIndex, 'videoFileLengthId'], clob?.id);
          }
          break;
        }
        case includes(['topicKeyword'], attributeType): {
          if (clob?.content) {
            acc[attributeType] = { ...clob, content: split(clob.content, ',') };
          }
          break;
        }
        case includes(['text'], attributeType): {
          if (clob?.content) {
            set(acc, ['text', groupIndex, attributeType], clob.content);
            set(acc, ['text', groupIndex, 'textId'], clob?.id);
            set(acc, ['text', groupIndex, 'groupSequence'], clob.groupSequence);
            set(
              acc,
              ['text', groupIndex, 'resourceCategoryId'],
              clob.resourceCategoryId,
            );
            set(
              acc,
              ['text', groupIndex, 'contentClobTypeId'],
              clob.contentClobTypeId,
            );
          }
          break;
        }
        case includes(['textKeyword'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['text', groupIndex, 'contentKeyword'],
              split(clob.content, ','),
            );
            set(acc, ['text', groupIndex, 'contentKeywordId'], clob?.id);
          }
          break;
        }
        case includes(['url', 'urlName', 'urlCaption'], attributeType): {
          if (clob?.content) {
            set(acc, ['url', groupIndex, attributeType], clob.content);
            set(acc, ['url', groupIndex, `${attributeType}Id`], clob?.id);
          }
          set(acc, ['url', groupIndex, 'groupSequence'], clob.groupSequence);
          if (includes(['url'], attributeType)) {
            const resourceCategoryId = clob.resourceCategoryId;
            set(
              acc,
              ['url', groupIndex, 'resourceCategoryId'],
              resourceCategoryId,
            );
          }
          break;
        }
        case includes(['urlKeyword'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['url', groupIndex, attributeType],
              split(clob.content, ','),
            );
            set(acc, ['url', groupIndex, `${attributeType}Id`], clob?.id);
          }
          break;
        }
        case includes(['questionTitle'], attributeType): {
          if (clob?.content) {
            set(
              acc,
              ['questionTitle', clob.groupSequence, attributeType],
              clob.content,
            );
          }
          break;
        }

        default: {
          acc[attributeType] = clob;
        }
      }
      return acc;
    },
    {},
  );
};
