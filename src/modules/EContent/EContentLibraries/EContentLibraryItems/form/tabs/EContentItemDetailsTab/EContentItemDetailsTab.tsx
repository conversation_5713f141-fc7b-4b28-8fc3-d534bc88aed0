import React, { useCallback, useMemo, useState } from 'react';
import { map, filter, includes, every, some, isEqual } from 'lodash';

import useT from '../../../../../../../common/components/utils/Translations/useT';
import EntityForm from '../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import StatusWithDraftField from '../../../../../../../common/components/containers/EntityForm/fields/StatusWithDraftField';
import TextAreaField from '../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import { IEContentItemForm } from '../../EContentItemForm';
import IEContentItem from '../../../../../../../common/abstract/EContent/IEContentItem';
import EContentLibraryResourcesTreeSelectorField from '../../../../../../../common/components/containers/EntityForm/fields/EContentLibraryResourcesTreeSelectorField';
import {
  isEContentLibraryFolder,
  isEContentLibraryResource,
} from '../../../../../../../model/EContentLibraryResourcesTypeNames';
import { Active, Draft } from '../../../../../../../model/StatusWithDraft';
import { MainAttributeIds } from '../../../../../../../model/EContentResourceXAttributeTypes';
import { ACTIVE } from '../../../../../../../model/IntakeStatuses';

const MAX_ITEM_NAME_LENGTH = 150;

const EContentItemDetailsTab: React.FC<IEContentItemForm> = ({
  entity,
  onGoBack,
  onSubmit,
  onDelete,
  libraryId,
  libraryName,
}) => {
  const t = useT();
  const isNew = useMemo(() => !entity?.id, [entity]);
  const _onSubmit = useCallback(
    async ({ id, name, description, status, resource }) => {
      await onSubmit({
        id,
        resourceId: resource?.id,
        name,
        description,
        status,
      } as IEContentItem);
    },
    [onSubmit],
  );

  const [isDisabled, setIsDisabled] = useState(true);

  const cookModels = useCallback(
    items => {
      if (isNew) {
        setIsDisabled(false);
        return items;
      }

      const { resource } = entity;
      const activeAttributeIds = map(
        filter(resource.attributes, { status: ACTIVE.value }),
        'attributeId',
      );
      const filteredArray = filter(MainAttributeIds, item =>
        includes(activeAttributeIds, item),
      );
      const matchingItems = filter(items, item => {
        if (isEContentLibraryFolder(item)) return true;
        if (isEContentLibraryResource(item)) {
          const activeAttr = map(
            filter(item.attributes, { status: ACTIVE.value }),
            'attributeId',
          );
          const filtered = filter(MainAttributeIds, item =>
            includes(activeAttr, item),
          );
          if (isEqual(filteredArray, filtered)) return true;
          return false;
        }
        return false;
      });
      const matchingItemsRes = filter(matchingItems, item =>
        isEContentLibraryResource(item),
      );
      if (matchingItemsRes.length > 1) {
        setIsDisabled(false);
      }
      return matchingItems;
    },
    [entity, isNew],
  );

  return (
    <EntityForm
      entity={entity}
      title={t('Details')}
      onCancel={isNew ? onGoBack : undefined}
      onDelete={onDelete}
      onGoBack={isNew ? undefined : onGoBack}
      onSubmit={_onSubmit}
    >
      <EntityFormFieldSet>
        <EntityNameField
          columns={4}
          label={t('Item Name')}
          maxLength={MAX_ITEM_NAME_LENGTH}
        />
        <EContentLibraryResourcesTreeSelectorField
          canRenderAdapter
          enableLibraryFilter
          columns={4}
          cookModels={cookModels}
          gqlVariables={{ status: [Active.value, Draft.value] }}
          isDisabled={isDisabled}
          label={t('Resource')}
          libraryId={Number(libraryId)}
          name="resource"
          nodeIsSelectable={isEContentLibraryResource}
          syntheticRootNodeName={libraryName}
        />
        <StatusWithDraftField columns={4} />
      </EntityFormFieldSet>
      <EntityFormFieldSet>
        <TextAreaField
          columns={1}
          label={t('Description')}
          name="description"
        />
      </EntityFormFieldSet>
    </EntityForm>
  );
};

export default EContentItemDetailsTab;
